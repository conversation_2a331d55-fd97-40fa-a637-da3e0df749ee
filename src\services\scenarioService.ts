// API Base URL - loaded from .env file
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Types for scenario management
export interface ScenarioSummary {
  scenario_id: number;
  policy_id: number;
  illustration_id: number;
  date_of_illustration: string;
  illustration_type_id: number;
  illustration_type_description: string;
  illustration_question_id: number;
  illustration_question_description: string;
  illustration_option_id?: number;
  illustration_option_description?: string;
  illustration_starting_age: number;
  illustration_ending_age: number;
  new_face_amount: number;
  new_coverage_option: string;
  new_premium_amount: number;
  new_loan_amount: number;
  new_loan_repayment_amount: number;
  current_interest_rate: number;
  guaranteed_minimum_rate: number;
  illustration_interest_rate: number;
  surrender_amount: number;
  retirement_age_goal?: number;
  is_schedule?: 'YES' | 'NO';
}

export interface GetPolicyScenariosResponse {
  policy_id: number;
  scenarios: ScenarioSummary[];
  total_count: number;
}

export interface ScenarioApiResponse {
  status: 'SUCCESS' | 'FAILED';
  message?: string;
  scenario_id?: number;
}

// Map illustration type IDs to category names
const ILLUSTRATION_TYPE_CATEGORY_MAP: Record<number, string> = {
  1: 'as-is-saved', // Changed from 'as-is' to 'as-is-saved' so it's not filtered out
  2: 'face-amount',
  3: 'premium',
  4: 'interest-rate', // Fixed: Type 4 is Interest Rate
  5: 'income',        // Fixed: Type 5 is Income (Full Surrender/Income)
  6: 'loan-repayment' // Fixed: Type 6 is Loan Repayment
};

// Map category names to display labels
const CATEGORY_LABELS: Record<string, string> = {
  'as-is': 'AS-IS',
  'as-is-saved': 'AS-IS', // Same display label as 'as-is'
  'face-amount': 'Face Amount',
  'premium': 'Premium',
  'interest-rate': 'Interest Rate',
  'income': 'Full Surrender / Income',
  'loan-repayment': 'Loan Repayment',
};

/**
 * Convert backend scenario data to frontend Scenario format
 */
export function convertBackendScenarioToFrontend(backendScenario: ScenarioSummary): any {
  const category = ILLUSTRATION_TYPE_CATEGORY_MAP[backendScenario.illustration_type_id] || 'unknown';

  // Helper function to safely format numbers
  const formatNumber = (value: number | null | undefined): string => {
    if (value === null || value === undefined || isNaN(value)) {
      return 'N/A';
    }
    return value.toLocaleString();
  };

  // Helper function to safely format currency
  const formatCurrency = (value: number | null | undefined): string => {
    if (value === null || value === undefined || isNaN(value)) {
      return 'N/A';
    }
    return `$${value.toLocaleString()}`;
  };

  // Helper function to safely format age range
  const formatAgeRange = (startAge: number | null | undefined, endAge: number | null | undefined): string => {
    const start = startAge && !isNaN(startAge) ? startAge.toString() : 'N/A';
    const end = endAge && !isNaN(endAge) ? endAge.toString() : 'N/A';
    return `${start} - ${end}`;
  };

  return {
    id: backendScenario.scenario_id.toString(),
    name: `${backendScenario.illustration_type_description || 'Unknown Type'}`, // Removed date
    policyId: backendScenario.policy_id.toString(),
    asIsDetails: backendScenario.illustration_question_description || '',
    whatIfOptions: backendScenario.illustration_option_description ? [backendScenario.illustration_option_description] : [],
    category: category,
    keyPoints: [
      `Type: ${backendScenario.illustration_type_description || 'Unknown'}`,
      // Removed age range, face amount, premium as requested
      // Keep only selected options with their values
      (backendScenario.new_loan_amount && backendScenario.new_loan_amount > 0) ? `Selected Option: New Loan Amount - ${formatCurrency(backendScenario.new_loan_amount)}` : '',
      (backendScenario.new_loan_repayment_amount && backendScenario.new_loan_repayment_amount > 0) ? `Selected Option: Loan Repayment - ${formatCurrency(backendScenario.new_loan_repayment_amount)}` : '',
      (backendScenario.surrender_amount && backendScenario.surrender_amount > 0) ? `Selected Option: Surrender Amount - ${formatCurrency(backendScenario.surrender_amount)}` : '',
      backendScenario.retirement_age_goal ? `Selected Option: Retirement Goal - Age ${backendScenario.retirement_age_goal}` : '',
      (backendScenario.current_interest_rate && backendScenario.current_interest_rate > 0) ? `Selected Option: Current Rate - ${(backendScenario.current_interest_rate * 100).toFixed(2)}%` : '',
      (backendScenario.guaranteed_minimum_rate && backendScenario.guaranteed_minimum_rate > 0) ? `Selected Option: Guaranteed Rate - ${(backendScenario.guaranteed_minimum_rate * 100).toFixed(2)}%` : '',
      backendScenario.illustration_starting_age ? `Selected Option: Starting Age - ${backendScenario.illustration_starting_age}` : '',
      backendScenario.illustration_option_description ? `Selected Option: ${backendScenario.illustration_option_description}` : '',
    ].filter(Boolean),
    data: {
      backendData: backendScenario,
      timestamp: backendScenario.date_of_illustration
    },
    createdAt: new Date(backendScenario.date_of_illustration),
    updatedAt: new Date(backendScenario.date_of_illustration),
  };
}

/**
 * Fetch all scenarios for a specific policy from the backend
 */
export async function fetchPolicyScenarios(policyId: number): Promise<any[]> {
  try {
    console.log('🔍 Fetching scenarios for policy ID:', policyId);

    const response = await fetch(`${API_BASE_URL}/get_policy_scenarios?policy_id=${policyId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: GetPolicyScenariosResponse = await response.json();
    console.log('📊 Backend response:', data);

    // Convert backend scenarios to frontend format
    const frontendScenarios = data.scenarios.map(convertBackendScenarioToFrontend);
    
    console.log('✅ Converted scenarios:', frontendScenarios);
    return frontendScenarios;

  } catch (error) {
    console.error('❌ Error fetching policy scenarios:', error);
    throw new Error(`Failed to fetch scenarios: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Check if a scenario was successfully saved by verifying it exists in the backend
 */
export async function verifyScenarioSaved(policyId: number, expectedCount?: number): Promise<boolean> {
  try {
    const scenarios = await fetchPolicyScenarios(policyId);
    
    if (expectedCount !== undefined) {
      return scenarios.length >= expectedCount;
    }
    
    return scenarios.length > 0;
  } catch (error) {
    console.error('❌ Error verifying scenario save:', error);
    return false;
  }
}

/**
 * Get the category label for display
 */
export function getCategoryLabel(category: string): string {
  return CATEGORY_LABELS[category] || category;
}

/**
 * Get scenarios grouped by category
 */
export function groupScenariosByCategory(scenarios: any[]): Record<string, any[]> {
  const grouped: Record<string, any[]> = {};

  scenarios.forEach(scenario => {
    const category = scenario.category;
    if (!grouped[category]) {
      grouped[category] = [];
    }
    grouped[category].push(scenario);
  });

  return grouped;
}

// ===== TABLE DATA TYPES =====

export interface ScenarioTableData {
  policyYear: number;
  endOfAge: number;
  plannedPremium: number;
  netOutlay: number;
  netSurrenderValue: number;
  netDeathBenefit: number;
}

// ===== CHART DATA SERVICES =====

export interface ChartDataPoint {
  year: number;
  age: number;
  'Planned Premium': number;
  'Net Outlay': number;
  'Net Surrender Value': number;
  'Net Death Benefit': number;
}

export interface PieChartDataPoint {
  name: string;
  value: number;
  color: string;
}

/**
 * Prepare chart data for visualization from table data
 * @param tableData - The scenario table data
 * @returns Chart data formatted for recharts
 */
export function prepareChartData(tableData: ScenarioTableData[]): ChartDataPoint[] {
  return tableData.map(row => ({
    year: row.policyYear,
    age: row.endOfAge,
    'Planned Premium': row.plannedPremium,
    'Net Outlay': row.netOutlay,
    'Net Surrender Value': row.netSurrenderValue,
    'Net Death Benefit': row.netDeathBenefit
  }));
}

/**
 * Prepare pie chart data from the latest year data
 * @param tableData - The scenario table data
 * @param chartColors - Color configuration for charts
 * @returns Pie chart data formatted for recharts
 */
export function preparePieChartData(tableData: ScenarioTableData[], chartColors: any): PieChartDataPoint[] {
  if (tableData.length === 0) return [];

  const latestData = tableData[tableData.length - 1];
  return [
    { name: 'Planned Premium', value: latestData.plannedPremium, color: chartColors.plannedPremium },
    { name: 'Net Outlay', value: latestData.netOutlay, color: chartColors.netOutlay },
    { name: 'Net Surrender Value', value: latestData.netSurrenderValue, color: chartColors.netSurrenderValue },
    { name: 'Net Death Benefit', value: latestData.netDeathBenefit, color: chartColors.netDeathBenefit }
  ];
}

// ===== KEY METRICS CALCULATION SERVICES =====

export interface KeyMetrics {
  totalPremiums: number;
  totalOutlay: number;
  finalSurrenderValue: number;
  finalDeathBenefit: number;
  netGain: number;
  roi: number;
}

/**
 * Calculate key performance metrics from table data
 * @param tableData - The scenario table data
 * @returns Calculated key metrics
 */
export function calculateKeyMetrics(tableData: ScenarioTableData[]): KeyMetrics {
  if (tableData.length === 0) {
    return {
      totalPremiums: 0,
      totalOutlay: 0,
      finalSurrenderValue: 0,
      finalDeathBenefit: 0,
      netGain: 0,
      roi: 0
    };
  }

  const lastYear = tableData[tableData.length - 1];
  const totalPremiums = tableData.reduce((sum: number, row: ScenarioTableData) => sum + row.plannedPremium, 0);
  const totalOutlay = tableData.reduce((sum: number, row: ScenarioTableData) => sum + row.netOutlay, 0);
  const finalSurrenderValue = lastYear.netSurrenderValue;
  const finalDeathBenefit = lastYear.netDeathBenefit;
  const netGain = finalSurrenderValue - totalOutlay;
  const roi = totalOutlay > 0 ? ((netGain / totalOutlay) * 100) : 0;

  return {
    totalPremiums,
    totalOutlay,
    finalSurrenderValue,
    finalDeathBenefit,
    netGain,
    roi
  };
}


