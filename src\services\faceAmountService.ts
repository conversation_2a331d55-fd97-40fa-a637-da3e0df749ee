/**
 * Face Amount Illustration Service - Backend API Integration
 * 
 * This service handles saving Face Amount illustration data to the database
 * using the backend API endpoint for storing illustration scenarios.
 */

// API Base URL - loaded from .env file
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

/**
 * Interface for Face Amount illustration data to be saved
 */
export interface FaceAmountIllustrationData {
  policy_id: number;
  current_death_benefit: number;
  current_death_benefit_option: string; // 'A' or 'B'
  want_to_change: boolean;
  change_immediately: boolean;
  change_amount: number;
  change_option_a_to_b: boolean;
  change_option_b_to_a: boolean;
  option_change_now: boolean;
  option_change_age: number;
  // Schedule data for modify by year
  schedule_data?: Array<{
    age: number;
    policy_year: number;
    calendar_year: number;
    face_amount: number;
  }>;
  // Section 2 data (Option A to B)
  section2_data?: {
    selected_type: 'age' | 'policyYear' | 'calendarYear' | null;
    start_value: number;
  };
  // Section 3 data (Option B to A)
  section3_data?: {
    selected_type: 'age' | 'policyYear' | 'calendarYear' | null;
    start_value: number;
  };
}

/**
 * Interface for the API request payload
 */
interface FaceAmountApiRequest {
  selected_options: Array<{
    policy_id: number;
    illustration_type_id: number;
    illustration_question_id: number;
    illustration_option_id?: number;
    illustration_starting_age?: number;
    illustration_ending_age?: number;
    new_face_amount?: number;
    new_coverage_option?: string;
    new_premium_amount?: number;
    new_loan_amount?: number;
    new_loan_repayment_amount?: number;
    current_interest_rate?: number;
    guaranteed_interest_rate?: number;
    illustration_interest_rate?: number;
    surrender_amount?: number;
    RETIREMENT_AGE_GOAL?: number;
    is_schedule?: 'YES' | 'NO';
    schedule_object?: Array<{
      age?: number;
      policy_year?: number;
      current_year?: number;
      face_amount?: number;
      premium_amount?: number;
      coverage_options?: string;
      loan_amount?: number;
      surrender_amount?: number;
      loan_repayment_amount?: number;
      illustration_interest_rate?: number;
    }>;
  }>;
}

/**
 * Interface for API response
 */
interface FaceAmountApiResponse {
  status: 'SUCCESS' | 'FAILED';
  message?: string;
}

/**
 * Map Face Amount data to API request format
 */
function mapFaceAmountDataToApiRequest(
  data: FaceAmountIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): FaceAmountApiRequest {
  const selected_options = [];

  console.log('🔍 Mapping Face Amount data to API request:', {
    want_to_change: data.want_to_change,
    change_immediately: data.change_immediately,
    change_option_a_to_b: data.change_option_a_to_b,
    change_option_b_to_a: data.change_option_b_to_a,
    current_death_benefit: data.current_death_benefit,
    change_amount: data.change_amount
  });

  // Question 201: Current Face Amount Death Benefit (first scenario)
  if (data.want_to_change || data.change_immediately) {
    const option201 = {
      policy_id: data.policy_id,
      illustration_type_id: 2, // Face Amount type
      illustration_question_id: 201,
      illustration_option_id: data.change_immediately ? 20101 : 20102,
      illustration_starting_age: undefined,
      illustration_ending_age: undefined,
      new_face_amount: data.change_immediately ? data.change_amount : data.current_death_benefit,
      new_coverage_option: data.current_death_benefit_option,
      new_premium_amount: undefined,
      new_loan_amount: undefined,
      new_loan_repayment_amount: undefined,
      current_interest_rate: undefined,
      guaranteed_interest_rate: undefined,
      illustration_interest_rate: undefined,
      surrender_amount: undefined,
      RETIREMENT_AGE_GOAL: undefined,
      is_schedule: data.want_to_change ? 'YES' : 'NO',
      schedule_object: data.want_to_change && data.schedule_data ?
        data.schedule_data.map(item => ({
          age: item.age,
          policy_year: item.policy_year,
          current_year: item.calendar_year,
          face_amount: item.face_amount,
          coverage_options: data.current_death_benefit_option
        })) : null,
      value: undefined
    };
    selected_options.push(option201);
  }

  // Question 202: Option A to B change (second scenario)
  if (data.change_option_a_to_b) {
    // Determine starting age - use section data if available, otherwise use option_change_age
    let startingAge = data.option_change_now ? currentAge : data.option_change_age;
    if (!data.option_change_now && data.section2_data && data.section2_data.selected_type === 'age') {
      startingAge = data.section2_data.start_value;
    }

    const option202 = {
      policy_id: data.policy_id,
      illustration_type_id: 2,
      illustration_question_id: 202,
      illustration_option_id: data.option_change_now ? 20201 : 20202,
      illustration_starting_age: startingAge,
      illustration_ending_age: undefined,
      new_face_amount: data.current_death_benefit,
      new_coverage_option: 'B', // Changing to Option B
      new_premium_amount: undefined,
      new_loan_amount: undefined,
      new_loan_repayment_amount: undefined,
      current_interest_rate: undefined,
      guaranteed_interest_rate: undefined,
      illustration_interest_rate: undefined,
      surrender_amount: undefined,
      RETIREMENT_AGE_GOAL: undefined,
      is_schedule: 'NO' as const,
      schedule_object: undefined,
      value: undefined
    };
    selected_options.push(option202);
  }

  // Question 203: Option B to A change (third scenario)
  if (data.change_option_b_to_a) {
    // Determine starting age - use section data if available, otherwise use option_change_age
    let startingAge = data.option_change_now ? currentAge : data.option_change_age;
    if (!data.option_change_now && data.section3_data && data.section3_data.selected_type === 'age') {
      startingAge = data.section3_data.start_value;
    }

    const option203 = {
      policy_id: data.policy_id,
      illustration_type_id: 2,
      illustration_question_id: 203,
      illustration_option_id: data.option_change_now ? 20301 : 20302,
      illustration_starting_age: startingAge,
      illustration_ending_age: undefined,
      new_face_amount: data.current_death_benefit,
      new_coverage_option: 'A', // Changing to Option A
      new_premium_amount: undefined,
      new_loan_amount: undefined,
      new_loan_repayment_amount: undefined,
      current_interest_rate: undefined,
      guaranteed_interest_rate: undefined,
      illustration_interest_rate: undefined,
      surrender_amount: undefined,
      RETIREMENT_AGE_GOAL: undefined,
      is_schedule: 'NO' as const,
      schedule_object: undefined,
      value: undefined
    };
    selected_options.push(option203);
  }

  // Ensure at least one option is sent - if no changes selected, send current state
  if (selected_options.length === 0) {
    const defaultOption = {
      policy_id: data.policy_id,
      illustration_type_id: 2,
      illustration_question_id: 201,
      illustration_option_id: 20101, // Current state option
      illustration_starting_age: undefined,
      illustration_ending_age: undefined,
      new_face_amount: data.current_death_benefit,
      new_coverage_option: data.current_death_benefit_option,
      new_premium_amount: undefined,
      new_loan_amount: undefined,
      new_loan_repayment_amount: undefined,
      current_interest_rate: undefined,
      guaranteed_interest_rate: undefined,
      illustration_interest_rate: undefined,
      surrender_amount: undefined,
      RETIREMENT_AGE_GOAL: undefined,
      is_schedule: 'NO' as const,
      schedule_object: undefined,
      value: undefined
    };
    selected_options.push(defaultOption);
  }

  return { selected_options };
}

/**
 * Save Face Amount illustration data to the database
 */
export async function saveFaceAmountIllustration(
  data: FaceAmountIllustrationData,
  currentAge: number,
  currentPolicyYear: number
): Promise<FaceAmountApiResponse> {
  try {
    console.log('🔍 Saving Face Amount illustration data:', data);
    console.log('📊 Current Age:', currentAge, 'Policy Year:', currentPolicyYear);

    // Map the data to API request format
    const apiRequest = mapFaceAmountDataToApiRequest(data, currentAge, currentPolicyYear);

    console.log('📤 API Request payload:', JSON.stringify(apiRequest, null, 2));
    console.log('🚀 Making API call to:', `${API_BASE_URL}/api/illustration/store_options`);

    // Make API call to backend
    const response = await fetch(`${API_BASE_URL}/api/illustration/store_options`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiRequest),
    });

    console.log('📡 Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Backend error response:', errorText);

      // Return a failed response instead of throwing to allow better error handling
      return {
        status: 'FAILED',
        message: `HTTP ${response.status}: ${errorText}`
      };
    }

    const responseText = await response.text();
    console.log('📄 Raw response:', responseText);

    let result: FaceAmountApiResponse;
    try {
      result = responseText ? JSON.parse(responseText) : { status: 'FAILED', message: 'Empty response' };
    } catch (parseError) {
      console.error('❌ Failed to parse response JSON:', parseError);
      return {
        status: 'FAILED',
        message: 'Invalid JSON response from server'
      };
    }

    console.log('✅ Face Amount illustration API response:', result);
    return result;

  } catch (error) {
    console.error('❌ Error saving Face Amount illustration:', error);
    return {
      status: 'FAILED',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Validate Face Amount data before saving
 */
export function validateFaceAmountData(data: FaceAmountIllustrationData): string[] {
  const errors: string[] = [];

  console.log('🔍 Validating Face Amount data:', data);

  if (!data.policy_id || data.policy_id <= 0) {
    errors.push('Policy ID is required and must be greater than 0');
  }

  if (!data.current_death_benefit || data.current_death_benefit <= 0) {
    errors.push('Current death benefit must be greater than 0');
  }

  // Only validate change amount if user wants to change immediately
  if (data.want_to_change && data.change_immediately && (!data.change_amount || data.change_amount <= 0)) {
    errors.push('New face amount must be greater than 0 when changing immediately');
  }

  // Only validate option change age if user is changing options and not doing it now
  // Allow validation to pass if section data is provided (handled in frontend)
  if ((data.change_option_a_to_b || data.change_option_b_to_a) &&
      !data.option_change_now &&
      (!data.option_change_age || data.option_change_age <= 0) &&
      (!data.section2_data && !data.section3_data)) {
    errors.push('Option change age must be specified when not changing immediately');
  }

  if (!['A', 'B'].includes(data.current_death_benefit_option)) {
    errors.push('Death benefit option must be A or B');
  }

  // Log validation results
  if (errors.length > 0) {
    console.warn('❌ Face Amount validation errors:', errors);
  } else {
    console.log('✅ Face Amount validation passed');
  }

  return errors;
}
